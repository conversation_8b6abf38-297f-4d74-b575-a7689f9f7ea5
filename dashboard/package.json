{"name": "tenderly-clone", "version": "0.1.0", "private": true, "dependencies": {"@monaco-editor/react": "^4.6.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@types/styled-components": "^5.1.34", "axios": "^1.7.9", "ethers": "^6.14.3", "monaco-editor": "^0.52.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-scripts": "5.0.1", "styled-components": "^6.1.18", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "recharts": "^2.8.0", "react-hot-toast": "^2.4.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}