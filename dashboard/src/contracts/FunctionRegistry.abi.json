[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "ADMIN_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "DEFAULT_ADMIN_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "DEVELOPER_ROLE", "inputs": [], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "addTrigger", "inputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}, {"name": "triggerType", "type": "uint8", "internalType": "enum FunctionRegistry.TriggerType"}, {"name": "triggerData", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "triggerId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "deactivateFunction", "inputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "executionHistory", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}, {"name": "triggerId", "type": "uint256", "internalType": "uint256"}, {"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}, {"name": "gasUsed", "type": "uint256", "internalType": "uint256"}, {"name": "timestamp", "type": "uint256", "internalType": "uint256"}, {"name": "errorMessage", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "fireTrigger", "inputs": [{"name": "triggerId", "type": "uint256", "internalType": "uint256"}, {"name": "contextData", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "functions", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "wasmHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "description", "type": "string", "internalType": "string"}, {"name": "owner", "type": "address", "internalType": "address"}, {"name": "gasLimit", "type": "uint256", "internalType": "uint256"}, {"name": "active", "type": "bool", "internalType": "bool"}, {"name": "createdAt", "type": "uint256", "internalType": "uint256"}, {"name": "executionCount", "type": "uint256", "internalType": "uint256"}, {"name": "runtime", "type": "string", "internalType": "string"}], "stateMutability": "view"}, {"type": "function", "name": "getExecutionHistory", "inputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple[]", "internalType": "struct FunctionRegistry.ExecutionResult[]", "components": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}, {"name": "triggerId", "type": "uint256", "internalType": "uint256"}, {"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}, {"name": "gasUsed", "type": "uint256", "internalType": "uint256"}, {"name": "timestamp", "type": "uint256", "internalType": "uint256"}, {"name": "errorMessage", "type": "string", "internalType": "string"}]}], "stateMutability": "view"}, {"type": "function", "name": "getFunction", "inputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct FunctionRegistry.FunctionMetadata", "components": [{"name": "wasmHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "name", "type": "string", "internalType": "string"}, {"name": "description", "type": "string", "internalType": "string"}, {"name": "owner", "type": "address", "internalType": "address"}, {"name": "gasLimit", "type": "uint256", "internalType": "uint256"}, {"name": "active", "type": "bool", "internalType": "bool"}, {"name": "createdAt", "type": "uint256", "internalType": "uint256"}, {"name": "executionCount", "type": "uint256", "internalType": "uint256"}, {"name": "runtime", "type": "string", "internalType": "string"}]}], "stateMutability": "view"}, {"type": "function", "name": "getRoleAdmin", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}], "outputs": [{"name": "", "type": "bytes32", "internalType": "bytes32"}], "stateMutability": "view"}, {"type": "function", "name": "getTrigger", "inputs": [{"name": "triggerId", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "tuple", "internalType": "struct FunctionRegistry.TriggerRule", "components": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}, {"name": "triggerType", "type": "uint8", "internalType": "enum FunctionRegistry.TriggerType"}, {"name": "triggerData", "type": "bytes", "internalType": "bytes"}, {"name": "active", "type": "bool", "internalType": "bool"}, {"name": "lastTriggered", "type": "uint256", "internalType": "uint256"}, {"name": "triggerCount", "type": "uint256", "internalType": "uint256"}]}], "stateMutability": "view"}, {"type": "function", "name": "grantDeveloperRole", "inputs": [{"name": "developer", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "grantRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "hasRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "maxGasLimit", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "nextFunctionId", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "nextTriggerId", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "registerFunction", "inputs": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "description", "type": "string", "internalType": "string"}, {"name": "wasmHash", "type": "bytes32", "internalType": "bytes32"}, {"name": "gasLimit", "type": "uint256", "internalType": "uint256"}, {"name": "runtime", "type": "string", "internalType": "string"}], "outputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "renounceRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "callerConfirmation", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "reportExecution", "inputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}, {"name": "triggerId", "type": "uint256", "internalType": "uint256"}, {"name": "success", "type": "bool", "internalType": "bool"}, {"name": "returnData", "type": "bytes", "internalType": "bytes"}, {"name": "gasUsed", "type": "uint256", "internalType": "uint256"}, {"name": "errorMessage", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "revokeDeveloperRole", "inputs": [{"name": "developer", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "revokeRole", "inputs": [{"name": "role", "type": "bytes32", "internalType": "bytes32"}, {"name": "account", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "setMaxGasLimit", "inputs": [{"name": "newLimit", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "triggers", "inputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}, {"name": "triggerType", "type": "uint8", "internalType": "enum FunctionRegistry.TriggerType"}, {"name": "triggerData", "type": "bytes", "internalType": "bytes"}, {"name": "active", "type": "bool", "internalType": "bool"}, {"name": "lastTriggered", "type": "uint256", "internalType": "uint256"}, {"name": "triggerCount", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "updateFunction", "inputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}, {"name": "newWasmHash", "type": "bytes32", "internalType": "bytes32"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "FunctionDeactivated", "inputs": [{"name": "functionId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "owner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "FunctionExecuted", "inputs": [{"name": "functionId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "triggerId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "success", "type": "bool", "indexed": false, "internalType": "bool"}, {"name": "gasUsed", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "FunctionRegistered", "inputs": [{"name": "functionId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "owner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "name", "type": "string", "indexed": false, "internalType": "string"}, {"name": "wasmHash", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "FunctionUpdated", "inputs": [{"name": "functionId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "newWasmHash", "type": "bytes32", "indexed": false, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleAdminChanged", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "previousAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "newAdminRole", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "RoleGranted", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "RoleRevoked", "inputs": [{"name": "role", "type": "bytes32", "indexed": true, "internalType": "bytes32"}, {"name": "account", "type": "address", "indexed": true, "internalType": "address"}, {"name": "sender", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "TriggerAdded", "inputs": [{"name": "triggerId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "functionId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "triggerType", "type": "uint8", "indexed": false, "internalType": "enum FunctionRegistry.TriggerType"}], "anonymous": false}, {"type": "event", "name": "TriggerFired", "inputs": [{"name": "triggerId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "functionId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "triggerData", "type": "bytes", "indexed": false, "internalType": "bytes"}], "anonymous": false}, {"type": "error", "name": "AccessControlBadConfirmation", "inputs": []}, {"type": "error", "name": "AccessControlUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}, {"name": "neededRole", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "FunctionInactive", "inputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "FunctionNotFound", "inputs": [{"name": "functionId", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "GasLimitExceeded", "inputs": [{"name": "requested", "type": "uint256", "internalType": "uint256"}, {"name": "maximum", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidWasmHash", "inputs": [{"name": "hash", "type": "bytes32", "internalType": "bytes32"}]}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "TriggerInactive", "inputs": [{"name": "triggerId", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "TriggerNotFound", "inputs": [{"name": "triggerId", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "UnauthorizedAccess", "inputs": [{"name": "caller", "type": "address", "internalType": "address"}, {"name": "functionId", "type": "uint256", "internalType": "uint256"}]}]